{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "14eaa687e5b72051314408d7e6620576", "packages": [], "packages-dev": [{"name": "dealerdirect/phpcodesniffer-composer-installer", "version": "v1.0.0", "source": {"type": "git", "url": "https://github.com/PHPCSStandards/composer-installer.git", "reference": "4be43904336affa5c2f70744a348312336afd0da"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPCSStandards/composer-installer/zipball/4be43904336affa5c2f70744a348312336afd0da", "reference": "4be43904336affa5c2f70744a348312336afd0da", "shasum": ""}, "require": {"composer-plugin-api": "^1.0 || ^2.0", "php": ">=5.4", "squizlabs/php_codesniffer": "^2.0 || ^3.1.0 || ^4.0"}, "require-dev": {"composer/composer": "*", "ext-json": "*", "ext-zip": "*", "php-parallel-lint/php-parallel-lint": "^1.3.1", "phpcompatibility/php-compatibility": "^9.0", "yoast/phpunit-polyfills": "^1.0"}, "type": "composer-plugin", "extra": {"class": "PHPCSStandards\\Composer\\Plugin\\Installers\\PHPCodeSniffer\\Plugin"}, "autoload": {"psr-4": {"PHPCSStandards\\Composer\\Plugin\\Installers\\PHPCodeSniffer\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.frenck.nl", "role": "Developer / IT Manager"}, {"name": "Contributors", "homepage": "https://github.com/PHPCSStandards/composer-installer/graphs/contributors"}], "description": "PHP_CodeSniffer Standards Composer Installer Plugin", "homepage": "http://www.dealerdirect.com", "keywords": ["PHPCodeSniffer", "PHP_CodeSniffer", "code quality", "codesniffer", "composer", "installer", "phpcbf", "phpcs", "plugin", "qa", "quality", "standard", "standards", "style guide", "stylecheck", "tests"], "support": {"issues": "https://github.com/PHPCSStandards/composer-installer/issues", "source": "https://github.com/PHPCSStandards/composer-installer"}, "time": "2023-01-05T11:28:13+00:00"}, {"name": "fidry/cpu-core-counter", "version": "1.2.0", "source": {"type": "git", "url": "https://github.com/theofidry/cpu-core-counter.git", "reference": "8520451a140d3f46ac33042715115e290cf5785f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/theofidry/cpu-core-counter/zipball/8520451a140d3f46ac33042715115e290cf5785f", "reference": "8520451a140d3f46ac33042715115e290cf5785f", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"fidry/makefile": "^0.2.0", "fidry/php-cs-fixer-config": "^1.1.2", "phpstan/extension-installer": "^1.2.0", "phpstan/phpstan": "^1.9.2", "phpstan/phpstan-deprecation-rules": "^1.0.0", "phpstan/phpstan-phpunit": "^1.2.2", "phpstan/phpstan-strict-rules": "^1.4.4", "phpunit/phpunit": "^8.5.31 || ^9.5.26", "webmozarts/strict-phpunit": "^7.5"}, "type": "library", "autoload": {"psr-4": {"Fidry\\CpuCoreCounter\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Théo FIDRY", "email": "<EMAIL>"}], "description": "Tiny utility to get the number of CPU cores.", "keywords": ["CPU", "core"], "support": {"issues": "https://github.com/theofidry/cpu-core-counter/issues", "source": "https://github.com/theofidry/cpu-core-counter/tree/1.2.0"}, "funding": [{"url": "https://github.com/theofidry", "type": "github"}], "time": "2024-08-06T10:04:20+00:00"}, {"name": "jetbrains/phpstorm-stubs", "version": "v2024.3", "source": {"type": "git", "url": "https://github.com/JetBrains/phpstorm-stubs.git", "reference": "0e82bdfe850c71857ee4ee3501ed82a9fc5d043c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/JetBrains/phpstorm-stubs/zipball/0e82bdfe850c71857ee4ee3501ed82a9fc5d043c", "reference": "0e82bdfe850c71857ee4ee3501ed82a9fc5d043c", "shasum": ""}, "require-dev": {"friendsofphp/php-cs-fixer": "v3.64.0", "nikic/php-parser": "v5.3.1", "phpdocumentor/reflection-docblock": "5.6.0", "phpunit/phpunit": "11.4.3"}, "type": "library", "autoload": {"files": ["PhpStormStubsMap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "PHP runtime & extensions header files for PhpStorm", "homepage": "https://www.jetbrains.com/phpstorm", "keywords": ["autocomplete", "code", "inference", "inspection", "jetbrains", "phpstorm", "stubs", "type"], "support": {"source": "https://github.com/JetBrains/phpstorm-stubs/tree/v2024.3"}, "time": "2024-12-14T08:03:12+00:00"}, {"name": "lucasbustamante/stubz", "version": "0.1", "source": {"type": "git", "url": "https://github.com/Luc45/stubz.git", "reference": "ee6c870c201f1d168eb9c09d0428e14f654078ae"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Luc45/stubz/zipball/ee6c870c201f1d168eb9c09d0428e14f654078ae", "reference": "ee6c870c201f1d168eb9c09d0428e14f654078ae", "shasum": ""}, "require": {"fidry/cpu-core-counter": "^1.2", "php": "^8.2", "roave/better-reflection": "^6.0", "symfony/finder": "^6.0"}, "suggest": {"ext-pcntl": "For running in parallel.", "ext-posix": "For running in parallel."}, "bin": ["bin/stubz"], "type": "library", "autoload": {"psr-4": {"Stubz\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A command-line PHP stub generator using BetterReflection.", "support": {"issues": "https://github.com/Luc45/stubz/issues", "source": "https://github.com/Luc45/stubz/tree/0.1"}, "time": "2025-03-01T02:23:00+00:00"}, {"name": "nikic/php-parser", "version": "v5.4.0", "source": {"type": "git", "url": "https://github.com/nikic/PHP-Parser.git", "reference": "447a020a1f875a434d62f2a401f53b82a396e494"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nikic/PHP-Parser/zipball/447a020a1f875a434d62f2a401f53b82a396e494", "reference": "447a020a1f875a434d62f2a401f53b82a396e494", "shasum": ""}, "require": {"ext-ctype": "*", "ext-json": "*", "ext-tokenizer": "*", "php": ">=7.4"}, "require-dev": {"ircmaxell/php-yacc": "^0.0.7", "phpunit/phpunit": "^9.0"}, "bin": ["bin/php-parse"], "type": "library", "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "autoload": {"psr-4": {"PhpParser\\": "lib/Php<PERSON><PERSON>er"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>"}], "description": "A PHP parser written in PHP", "keywords": ["parser", "php"], "support": {"issues": "https://github.com/nikic/PHP-Parser/issues", "source": "https://github.com/nikic/PHP-Parser/tree/v5.4.0"}, "time": "2024-12-30T11:07:19+00:00"}, {"name": "php-stubs/wordpress-stubs", "version": "v6.8.1", "source": {"type": "git", "url": "https://github.com/php-stubs/wordpress-stubs.git", "reference": "92e444847d94f7c30f88c60004648f507688acd5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-stubs/wordpress-stubs/zipball/92e444847d94f7c30f88c60004648f507688acd5", "reference": "92e444847d94f7c30f88c60004648f507688acd5", "shasum": ""}, "conflict": {"phpdocumentor/reflection-docblock": "5.6.1"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^1.0", "nikic/php-parser": "^5.4", "php": "^7.4 || ^8.0", "php-stubs/generator": "^0.8.3", "phpdocumentor/reflection-docblock": "^5.4.1", "phpstan/phpstan": "^2.1", "phpunit/phpunit": "^9.5", "szepeviktor/phpcs-psr-12-neutron-hybrid-ruleset": "^1.1.1", "wp-coding-standards/wpcs": "3.1.0 as 2.3.0"}, "suggest": {"paragonie/sodium_compat": "Pure PHP implementation of libsodium", "symfony/polyfill-php80": "Symfony polyfill backporting some PHP 8.0+ features to lower PHP versions", "szepeviktor/phpstan-wordpress": "WordPress extensions for PHPStan"}, "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "WordPress function and class declaration stubs for static analysis.", "homepage": "https://github.com/php-stubs/wordpress-stubs", "keywords": ["PHPStan", "static analysis", "wordpress"], "support": {"issues": "https://github.com/php-stubs/wordpress-stubs/issues", "source": "https://github.com/php-stubs/wordpress-stubs/tree/v6.8.1"}, "time": "2025-05-02T12:33:34+00:00"}, {"name": "php-stubs/wp-cli-stubs", "version": "v2.11.0", "source": {"type": "git", "url": "https://github.com/php-stubs/wp-cli-stubs.git", "reference": "f27ff9e8e29d7962cb070e58de70dfaf63183007"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-stubs/wp-cli-stubs/zipball/f27ff9e8e29d7962cb070e58de70dfaf63183007", "reference": "f27ff9e8e29d7962cb070e58de70dfaf63183007", "shasum": ""}, "require": {"php-stubs/wordpress-stubs": "^4.7 || ^5.0 || ^6.0"}, "require-dev": {"php": "~7.3 || ~8.0", "php-stubs/generator": "^0.8.0"}, "suggest": {"symfony/polyfill-php73": "Symfony polyfill backporting some PHP 7.3+ features to lower PHP versions", "szepeviktor/phpstan-wordpress": "WordPress extensions for PHPStan"}, "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "WP-CLI function and class declaration stubs for static analysis.", "homepage": "https://github.com/php-stubs/wp-cli-stubs", "keywords": ["PHPStan", "static analysis", "wordpress", "wp-cli"], "support": {"issues": "https://github.com/php-stubs/wp-cli-stubs/issues", "source": "https://github.com/php-stubs/wp-cli-stubs/tree/v2.11.0"}, "time": "2024-11-25T10:09:13+00:00"}, {"name": "phpcompatibility/php-compatibility", "version": "9.3.5", "source": {"type": "git", "url": "https://github.com/PHPCompatibility/PHPCompatibility.git", "reference": "9fb324479acf6f39452e0655d2429cc0d3914243"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPCompatibility/PHPCompatibility/zipball/9fb324479acf6f39452e0655d2429cc0d3914243", "reference": "9fb324479acf6f39452e0655d2429cc0d3914243", "shasum": ""}, "require": {"php": ">=5.3", "squizlabs/php_codesniffer": "^2.3 || ^3.0.2"}, "conflict": {"squizlabs/php_codesniffer": "2.6.2"}, "require-dev": {"phpunit/phpunit": "~4.5 || ^5.0 || ^6.0 || ^7.0"}, "suggest": {"dealerdirect/phpcodesniffer-composer-installer": "^0.5 || This Composer plugin will sort out the PHPCS 'installed_paths' automatically.", "roave/security-advisories": "dev-master || Helps prevent installing dependencies with known security issues."}, "type": "phpcodesniffer-standard", "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON><PERSON>", "homepage": "https://github.com/wimg", "role": "lead"}, {"name": "<PERSON>", "homepage": "https://github.com/jrfnl", "role": "lead"}, {"name": "Contributors", "homepage": "https://github.com/PHPCompatibility/PHPCompatibility/graphs/contributors"}], "description": "A set of sniffs for PHP_CodeSniffer that checks for PHP cross-version compatibility.", "homepage": "http://techblog.wimgodden.be/tag/codesniffer/", "keywords": ["compatibility", "phpcs", "standards"], "support": {"issues": "https://github.com/PHPCompatibility/PHPCompatibility/issues", "source": "https://github.com/PHPCompatibility/PHPCompatibility"}, "time": "2019-12-27T09:44:58+00:00"}, {"name": "phpcompatibility/phpcompatibility-paragonie", "version": "1.3.3", "source": {"type": "git", "url": "https://github.com/PHPCompatibility/PHPCompatibilityParagonie.git", "reference": "293975b465e0e709b571cbf0c957c6c0a7b9a2ac"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPCompatibility/PHPCompatibilityParagonie/zipball/293975b465e0e709b571cbf0c957c6c0a7b9a2ac", "reference": "293975b465e0e709b571cbf0c957c6c0a7b9a2ac", "shasum": ""}, "require": {"phpcompatibility/php-compatibility": "^9.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^1.0", "paragonie/random_compat": "dev-master", "paragonie/sodium_compat": "dev-master"}, "suggest": {"dealerdirect/phpcodesniffer-composer-installer": "^1.0 || This Composer plugin will sort out the PHP_CodeSniffer 'installed_paths' automatically.", "roave/security-advisories": "dev-master || Helps prevent installing dependencies with known security issues."}, "type": "phpcodesniffer-standard", "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON><PERSON>", "role": "lead"}, {"name": "<PERSON>", "role": "lead"}], "description": "A set of rulesets for PHP_CodeSniffer to check for PHP cross-version compatibility issues in projects, while accounting for polyfills provided by the Paragonie polyfill libraries.", "homepage": "http://phpcompatibility.com/", "keywords": ["compatibility", "paragonie", "phpcs", "polyfill", "standards", "static analysis"], "support": {"issues": "https://github.com/PHPCompatibility/PHPCompatibilityParagonie/issues", "security": "https://github.com/PHPCompatibility/PHPCompatibilityParagonie/security/policy", "source": "https://github.com/PHPCompatibility/PHPCompatibilityParagonie"}, "funding": [{"url": "https://github.com/PHPCompatibility", "type": "github"}, {"url": "https://github.com/jrfnl", "type": "github"}, {"url": "https://opencollective.com/php_codesniffer", "type": "open_collective"}], "time": "2024-04-24T21:30:46+00:00"}, {"name": "phpcompatibility/phpcompatibility-wp", "version": "2.1.7", "source": {"type": "git", "url": "https://github.com/PHPCompatibility/PHPCompatibilityWP.git", "reference": "5bfbbfbabb3df2b9a83e601de9153e4a7111962c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPCompatibility/PHPCompatibilityWP/zipball/5bfbbfbabb3df2b9a83e601de9153e4a7111962c", "reference": "5bfbbfbabb3df2b9a83e601de9153e4a7111962c", "shasum": ""}, "require": {"phpcompatibility/php-compatibility": "^9.0", "phpcompatibility/phpcompatibility-paragonie": "^1.0", "squizlabs/php_codesniffer": "^3.3"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^1.0"}, "suggest": {"dealerdirect/phpcodesniffer-composer-installer": "^1.0 || This Composer plugin will sort out the PHP_CodeSniffer 'installed_paths' automatically.", "roave/security-advisories": "dev-master || Helps prevent installing dependencies with known security issues."}, "type": "phpcodesniffer-standard", "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON><PERSON>", "role": "lead"}, {"name": "<PERSON>", "role": "lead"}], "description": "A ruleset for PHP_CodeSniffer to check for PHP cross-version compatibility issues in projects, while accounting for polyfills provided by WordPress.", "homepage": "http://phpcompatibility.com/", "keywords": ["compatibility", "phpcs", "standards", "static analysis", "wordpress"], "support": {"issues": "https://github.com/PHPCompatibility/PHPCompatibilityWP/issues", "security": "https://github.com/PHPCompatibility/PHPCompatibilityWP/security/policy", "source": "https://github.com/PHPCompatibility/PHPCompatibilityWP"}, "funding": [{"url": "https://github.com/PHPCompatibility", "type": "github"}, {"url": "https://github.com/jrfnl", "type": "github"}, {"url": "https://opencollective.com/php_codesniffer", "type": "open_collective"}, {"url": "https://thanks.dev/u/gh/phpcompatibility", "type": "thanks_dev"}], "time": "2025-05-12T16:38:37+00:00"}, {"name": "phpcsstandards/phpcsextra", "version": "1.3.0", "source": {"type": "git", "url": "https://github.com/PHPCSStandards/PHPCSExtra.git", "reference": "46d08eb86eec622b96c466adec3063adfed280dd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPCSStandards/PHPCSExtra/zipball/46d08eb86eec622b96c466adec3063adfed280dd", "reference": "46d08eb86eec622b96c466adec3063adfed280dd", "shasum": ""}, "require": {"php": ">=5.4", "phpcsstandards/phpcsutils": "^1.0.9", "squizlabs/php_codesniffer": "^3.12.1"}, "require-dev": {"php-parallel-lint/php-console-highlighter": "^1.0", "php-parallel-lint/php-parallel-lint": "^1.3.2", "phpcsstandards/phpcsdevcs": "^1.1.6", "phpcsstandards/phpcsdevtools": "^1.2.1", "phpunit/phpunit": "^4.5 || ^5.0 || ^6.0 || ^7.0 || ^8.0 || ^9.0"}, "type": "phpcodesniffer-standard", "extra": {"branch-alias": {"dev-stable": "1.x-dev", "dev-develop": "1.x-dev"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON>", "homepage": "https://github.com/jrfnl", "role": "lead"}, {"name": "Contributors", "homepage": "https://github.com/PHPCSStandards/PHPCSExtra/graphs/contributors"}], "description": "A collection of sniffs and standards for use with PHP_CodeSniffer.", "keywords": ["PHP_CodeSniffer", "phpcbf", "phpcodesniffer-standard", "phpcs", "standards", "static analysis"], "support": {"issues": "https://github.com/PHPCSStandards/PHPCSExtra/issues", "security": "https://github.com/PHPCSStandards/PHPCSExtra/security/policy", "source": "https://github.com/PHPCSStandards/PHPCSExtra"}, "funding": [{"url": "https://github.com/PHPCSStandards", "type": "github"}, {"url": "https://github.com/jrfnl", "type": "github"}, {"url": "https://opencollective.com/php_codesniffer", "type": "open_collective"}, {"url": "https://thanks.dev/u/gh/phpcsstandards", "type": "thanks_dev"}], "time": "2025-04-20T23:35:32+00:00"}, {"name": "phpcsstandards/phpcsutils", "version": "1.0.12", "source": {"type": "git", "url": "https://github.com/PHPCSStandards/PHPCSUtils.git", "reference": "87b233b00daf83fb70f40c9a28692be017ea7c6c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPCSStandards/PHPCSUtils/zipball/87b233b00daf83fb70f40c9a28692be017ea7c6c", "reference": "87b233b00daf83fb70f40c9a28692be017ea7c6c", "shasum": ""}, "require": {"dealerdirect/phpcodesniffer-composer-installer": "^0.4.1 || ^0.5 || ^0.6.2 || ^0.7 || ^1.0", "php": ">=5.4", "squizlabs/php_codesniffer": "^3.10.0 || 4.0.x-dev@dev"}, "require-dev": {"ext-filter": "*", "php-parallel-lint/php-console-highlighter": "^1.0", "php-parallel-lint/php-parallel-lint": "^1.3.2", "phpcsstandards/phpcsdevcs": "^1.1.6", "yoast/phpunit-polyfills": "^1.1.0 || ^2.0.0"}, "type": "phpcodesniffer-standard", "extra": {"branch-alias": {"dev-stable": "1.x-dev", "dev-develop": "1.x-dev"}}, "autoload": {"classmap": ["PHPCSUtils/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON>", "homepage": "https://github.com/jrfnl", "role": "lead"}, {"name": "Contributors", "homepage": "https://github.com/PHPCSStandards/PHPCSUtils/graphs/contributors"}], "description": "A suite of utility functions for use with PHP_CodeSniffer", "homepage": "https://phpcsutils.com/", "keywords": ["PHP_CodeSniffer", "phpcbf", "phpcodesniffer-standard", "phpcs", "phpcs3", "standards", "static analysis", "tokens", "utility"], "support": {"docs": "https://phpcsutils.com/", "issues": "https://github.com/PHPCSStandards/PHPCSUtils/issues", "security": "https://github.com/PHPCSStandards/PHPCSUtils/security/policy", "source": "https://github.com/PHPCSStandards/PHPCSUtils"}, "funding": [{"url": "https://github.com/PHPCSStandards", "type": "github"}, {"url": "https://github.com/jrfnl", "type": "github"}, {"url": "https://opencollective.com/php_codesniffer", "type": "open_collective"}], "time": "2024-05-20T13:34:27+00:00"}, {"name": "phpstan/phpstan", "version": "2.1.16", "source": {"type": "git", "url": "https://github.com/phpstan/phpstan.git", "reference": "b8c1cf533cba0c305d91c6ccd23f3dd0566ba5f9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpstan/phpstan/zipball/b8c1cf533cba0c305d91c6ccd23f3dd0566ba5f9", "reference": "b8c1cf533cba0c305d91c6ccd23f3dd0566ba5f9", "shasum": ""}, "require": {"php": "^7.4|^8.0"}, "conflict": {"phpstan/phpstan-shim": "*"}, "bin": ["phpstan", "phpstan.phar"], "type": "library", "autoload": {"files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "PHPStan - PHP Static Analysis Tool", "keywords": ["dev", "static analysis"], "support": {"docs": "https://phpstan.org/user-guide/getting-started", "forum": "https://github.com/phpstan/phpstan/discussions", "issues": "https://github.com/phpstan/phpstan/issues", "security": "https://github.com/phpstan/phpstan/security/policy", "source": "https://github.com/phpstan/phpstan-src"}, "funding": [{"url": "https://github.com/ondrejmirtes", "type": "github"}, {"url": "https://github.com/phpstan", "type": "github"}], "time": "2025-05-16T09:40:10+00:00"}, {"name": "roave/better-reflection", "version": "6.57.0", "source": {"type": "git", "url": "https://github.com/Roave/BetterReflection.git", "reference": "d5fa8e106a1a046ea9b9a79e4ce95c6b6c158ae0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Roave/BetterReflection/zipball/d5fa8e106a1a046ea9b9a79e4ce95c6b6c158ae0", "reference": "d5fa8e106a1a046ea9b9a79e4ce95c6b6c158ae0", "shasum": ""}, "require": {"ext-json": "*", "jetbrains/phpstorm-stubs": "2024.3", "nikic/php-parser": "^5.4.0", "php": "~8.2.0 || ~8.3.2 || ~8.4.1"}, "conflict": {"thecodingmachine/safe": "<1.1.3"}, "require-dev": {"phpbench/phpbench": "^1.4.0", "phpunit/phpunit": "^11.5.7"}, "suggest": {"composer/composer": "Required to use the ComposerSourceLocator"}, "type": "library", "autoload": {"psr-4": {"Roave\\BetterReflection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/asgrim"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://ocramius.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/geeh"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/kukulich"}], "description": "Better Reflection - an improved code reflection API", "support": {"issues": "https://github.com/Roave/BetterReflection/issues", "source": "https://github.com/Roave/BetterReflection/tree/6.57.0"}, "time": "2025-02-12T20:28:58+00:00"}, {"name": "squizlabs/php_codesniffer", "version": "3.13.0", "source": {"type": "git", "url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "reference": "65ff2489553b83b4597e89c3b8b721487011d186"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/65ff2489553b83b4597e89c3b8b721487011d186", "reference": "65ff2489553b83b4597e89c3b8b721487011d186", "shasum": ""}, "require": {"ext-simplexml": "*", "ext-tokenizer": "*", "ext-xmlwriter": "*", "php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "^4.0 || ^5.0 || ^6.0 || ^7.0 || ^8.0 || ^9.3.4"}, "bin": ["bin/phpcbf", "bin/phpcs"], "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "role": "Former lead"}, {"name": "<PERSON>", "role": "Current lead"}, {"name": "Contributors", "homepage": "https://github.com/PHPCSStandards/PHP_CodeSniffer/graphs/contributors"}], "description": "PHP_CodeSniffer tokenizes PHP, JavaScript and CSS files and detects violations of a defined set of coding standards.", "homepage": "https://github.com/PHPCSStandards/PHP_CodeSniffer", "keywords": ["phpcs", "standards", "static analysis"], "support": {"issues": "https://github.com/PHPCSStandards/PHP_CodeSniffer/issues", "security": "https://github.com/PHPCSStandards/PHP_CodeSniffer/security/policy", "source": "https://github.com/PHPCSStandards/PHP_CodeSniffer", "wiki": "https://github.com/PHPCSStandards/PHP_CodeSniffer/wiki"}, "funding": [{"url": "https://github.com/PHPCSStandards", "type": "github"}, {"url": "https://github.com/jrfnl", "type": "github"}, {"url": "https://opencollective.com/php_codesniffer", "type": "open_collective"}, {"url": "https://thanks.dev/u/gh/phpcsstandards", "type": "thanks_dev"}], "time": "2025-05-11T03:36:00+00:00"}, {"name": "symfony/finder", "version": "v6.4.17", "source": {"type": "git", "url": "https://github.com/symfony/finder.git", "reference": "1d0e8266248c5d9ab6a87e3789e6dc482af3c9c7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/finder/zipball/1d0e8266248c5d9ab6a87e3789e6dc482af3c9c7", "reference": "1d0e8266248c5d9ab6a87e3789e6dc482af3c9c7", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"symfony/filesystem": "^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Finds files and directories via an intuitive fluent interface", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/finder/tree/v6.4.17"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-12-29T13:51:37+00:00"}, {"name": "szepeviktor/phpstan-wordpress", "version": "v2.0.2", "source": {"type": "git", "url": "https://github.com/szepeviktor/phpstan-wordpress.git", "reference": "963887b04c21fe7ac78e61c1351f8b00fff9f8f8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/szepeviktor/phpstan-wordpress/zipball/963887b04c21fe7ac78e61c1351f8b00fff9f8f8", "reference": "963887b04c21fe7ac78e61c1351f8b00fff9f8f8", "shasum": ""}, "require": {"php": "^7.4 || ^8.0", "php-stubs/wordpress-stubs": "^6.6.2", "phpstan/phpstan": "^2.0"}, "require-dev": {"composer/composer": "^2.1.14", "dealerdirect/phpcodesniffer-composer-installer": "^1.0", "php-parallel-lint/php-parallel-lint": "^1.1", "phpstan/phpstan-strict-rules": "^2.0", "phpunit/phpunit": "^9.0", "szepeviktor/phpcs-psr-12-neutron-hybrid-ruleset": "^1.0", "wp-coding-standards/wpcs": "3.1.0 as 2.3.0"}, "suggest": {"swissspidy/phpstan-no-private": "Detect usage of internal core functions, classes and methods"}, "type": "phpstan-extension", "extra": {"phpstan": {"includes": ["extension.neon"]}}, "autoload": {"psr-4": {"SzepeViktor\\PHPStan\\WordPress\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "WordPress extensions for PHPStan", "keywords": ["PHPStan", "code analyse", "code analysis", "static analysis", "wordpress"], "support": {"issues": "https://github.com/szepeviktor/phpstan-wordpress/issues", "source": "https://github.com/szepeviktor/phpstan-wordpress/tree/v2.0.2"}, "time": "2025-02-12T18:43:37+00:00"}, {"name": "woocommerce/qit-cli", "version": "0.10.0", "source": {"type": "git", "url": "https://github.com/woocommerce/qit-cli.git", "reference": "42c4722bb71940dc0435103775439588e923e1cd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/woocommerce/qit-cli/zipball/42c4722bb71940dc0435103775439588e923e1cd", "reference": "42c4722bb71940dc0435103775439588e923e1cd", "shasum": ""}, "require": {"ext-curl": "*", "php": "^7.2.5 | ^8"}, "bin": ["qit"], "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["GPL-3.0-or-later"], "description": "A command line interface for WooCommerce Quality Insights Toolkit (QIT).", "support": {"issues": "https://github.com/woocommerce/qit-cli/issues", "source": "https://github.com/woocommerce/qit-cli/tree/0.10.0"}, "time": "2025-05-20T15:58:42+00:00"}, {"name": "woocommerce/woocommerce-sniffs", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/woocommerce/woocommerce-sniffs.git", "reference": "3a65b917ff5ab5e65609e5dcb7bc62f9455bbef8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/woocommerce/woocommerce-sniffs/zipball/3a65b917ff5ab5e65609e5dcb7bc62f9455bbef8", "reference": "3a65b917ff5ab5e65609e5dcb7bc62f9455bbef8", "shasum": ""}, "require": {"dealerdirect/phpcodesniffer-composer-installer": "^1.0.0", "php": ">=7.0", "phpcompatibility/phpcompatibility-wp": "^2.1.0", "wp-coding-standards/wpcs": "^3.0.0"}, "type": "phpcodesniffer-standard", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "WooCommerce sniffs", "keywords": ["phpcs", "standards", "static analysis", "woocommerce", "wordpress"], "support": {"issues": "https://github.com/woocommerce/woocommerce-sniffs/issues", "source": "https://github.com/woocommerce/woocommerce-sniffs/tree/1.0.0"}, "time": "2023-09-29T13:52:33+00:00"}, {"name": "wp-coding-standards/wpcs", "version": "3.1.0", "source": {"type": "git", "url": "https://github.com/WordPress/WordPress-Coding-Standards.git", "reference": "9333efcbff231f10dfd9c56bb7b65818b4733ca7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/WordPress/WordPress-Coding-Standards/zipball/9333efcbff231f10dfd9c56bb7b65818b4733ca7", "reference": "9333efcbff231f10dfd9c56bb7b65818b4733ca7", "shasum": ""}, "require": {"ext-filter": "*", "ext-libxml": "*", "ext-tokenizer": "*", "ext-xmlreader": "*", "php": ">=5.4", "phpcsstandards/phpcsextra": "^1.2.1", "phpcsstandards/phpcsutils": "^1.0.10", "squizlabs/php_codesniffer": "^3.9.0"}, "require-dev": {"php-parallel-lint/php-console-highlighter": "^1.0.0", "php-parallel-lint/php-parallel-lint": "^1.3.2", "phpcompatibility/php-compatibility": "^9.0", "phpcsstandards/phpcsdevtools": "^1.2.0", "phpunit/phpunit": "^4.0 || ^5.0 || ^6.0 || ^7.0 || ^8.0 || ^9.0"}, "suggest": {"ext-iconv": "For improved results", "ext-mbstring": "For improved results"}, "type": "phpcodesniffer-standard", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Contributors", "homepage": "https://github.com/WordPress/WordPress-Coding-Standards/graphs/contributors"}], "description": "PHP_CodeSniffer rules (sniffs) to enforce WordPress coding conventions", "keywords": ["phpcs", "standards", "static analysis", "wordpress"], "support": {"issues": "https://github.com/WordPress/WordPress-Coding-Standards/issues", "source": "https://github.com/WordPress/WordPress-Coding-Standards", "wiki": "https://github.com/WordPress/WordPress-Coding-Standards/wiki"}, "funding": [{"url": "https://opencollective.com/php_codesniffer", "type": "custom"}], "time": "2024-03-25T16:39:00+00:00"}], "aliases": [], "minimum-stability": "stable", "stability-flags": [], "prefer-stable": false, "prefer-lowest": false, "platform": [], "platform-dev": [], "plugin-api-version": "2.6.0"}