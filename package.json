{"name": "woocommerce-shipstation", "description": "Ship your WooCommerce orders with confidence, save on top carriers, and automate your processes with ShipStation.", "version": "4.7.4", "title": "ShipStation for WooCommerce", "devDependencies": {"clean-css-cli": "^4.2.1", "node-wp-i18n": "~1.2.3", "npm-run-all": "^4.1.5", "sass": "^1.77.5", "uglify-js": "^3.4.9"}, "scripts": {"prebuild": "rm -rf ./vendor", "build": "pnpm run build:prod && pnpm run archive", "build:dev": "pnpm run uglify && pnpm run makepot && pnpm run sass", "build:prod": "pnpm run uglify && pnpm run makepot && pnpm run sass", "archive": "composer archive --file=$npm_package_name --format=zip", "postarchive": "rm -rf $npm_package_name && unzip $npm_package_name.zip -d $npm_package_name && rm $npm_package_name.zip && zip -r $npm_package_name.zip $npm_package_name && rm -rf $npm_package_name", "preuglify": "rm -f $npm_package_config_paths_js_min", "uglify": "sh -c 'for f in $npm_package_config_paths_js; do if [ -s \"$f\" ]; then echo Processing \"$f\"; file=${f%.js}; node_modules/.bin/uglifyjs \"$f\" -c -m > \"$file.min.js\" || echo \"Uglify failed for $f\"; else echo \"Skipping. No js file to process.\"; fi; done'", "presass": "rm -f $npm_package_config_paths_css", "sass": "sass $npm_package_config_paths_sass:$npm_package_config_paths_cssfolder --no-source-map --style compressed", "watchsass": "sass $npm_package_config_paths_sass:$npm_package_config_paths_cssfolder --watch", "postsass": "for f in $npm_package_config_paths_css; do echo Processing $f; file=${f%.css}; node_modules/.bin/cleancss -o $file.css $f; done", "makepot": "wpi18n makepot --domain-path languages --pot-file $npm_package_name.pot --type plugin --main-file $npm_package_name.php --exclude node_modules,tests,docs", "build:phpstan": "composer install --no-dev -o && pnpm run makepot && pnpm run archive:phpstan && pnpm run postarchive", "archive:phpstan": "composer archive --file=$npm_package_name --format=zip && pnpm run zip:phpstan_config", "zip:phpstan_config": "zip -r $npm_package_name.zip .phpstan/dist/* -j", "build:qit": "pnpm run build:prod && pnpm run archive:qit", "archive:qit": "composer archive --file=$npm_package_config_wp_org_slug --format=zip && zip -r $npm_package_config_wp_org_slug.zip .phpstan/dist/* -j", "postarchive:qit": "rm -rf $npm_package_config_wp_org_slug && unzip $npm_package_config_wp_org_slug.zip -d $npm_package_config_wp_org_slug && rm $npm_package_config_wp_org_slug.zip && zip -r $npm_package_config_wp_org_slug.zip $npm_package_config_wp_org_slug && rm -rf $npm_package_config_wp_org_slug"}, "engines": {"node": "^22.14.0", "pnpm": "^10.4.1"}, "config": {"use_pnpm": true, "wp_org_slug": "woocommerce-shipstation-integration", "use_gh_release_notes": true, "paths": {"js": "assets/js/*.js", "js_min": "assets/js/*.min.js", "css": "assets/css/*.css", "sass": "assets/sass", "cssfolder": "assets/css"}}}