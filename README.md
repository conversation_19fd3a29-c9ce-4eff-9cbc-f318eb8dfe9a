[![CI](https://github.com/woocommerce/woocommerce-shipstation/actions/workflows/merge_to_trunk.yml/badge.svg)](https://github.com/woocommerce/woocommerce-shipstation/actions/workflows/merge_to_trunk.yml)
[![CI](https://github.com/woocommerce/woocommerce-shipstation/actions/workflows/cron_qit.yml/badge.svg)](https://github.com/woocommerce/woocommerce-shipstation/actions/workflows/cron_qit.yml)

woocommerce-shipstation
====================

Ship your WooCommerce orders with confidence, save on top carriers, and automate your processes with ShipStation.

## NPM Scripts

WooCommerce Shipstation utilizes npm scripts for task management utilities.

`pnpm run build` - Runs the tasks necessary for a release. These may include building JavaScript, SASS, CSS minification, and language files.
